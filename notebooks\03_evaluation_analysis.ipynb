{"cells": [{"cell_type": "markdown", "id": "92f044af", "metadata": {}, "source": ["# 模型评估分析\n", "\n", "本笔记本用于详细分析训练好的模型性能，包括：\n", "- 多维度指标分析\n", "- 混淆矩阵和ROC曲线\n", "- 预测置信度分析\n", "- 错误案例分析\n", "- 注意力机制可视化"]}, {"cell_type": "code", "execution_count": null, "id": "72fee7f1", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.join(os.getcwd(), '..', 'src'))\n", "sys.path.append(os.path.join(os.getcwd(), '..', 'models'))\n", "\n", "import torch\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 导入自定义模块\n", "from data_preprocessing import load_and_preprocess_data, create_data_loaders\n", "from model_evaluation import ModelEvaluator\n", "from base_model import BertForSequenceClassification as EnhancedBERTModel\n", "from improved_model import ImprovedSentimentModel as ImprovedBERTModel\n", "from utils import plot_confusion_matrix, plot_roc_curve, visualize_attention\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"导入完成!\")"]}, {"cell_type": "markdown", "id": "935ed288", "metadata": {}, "source": ["## 1. 加载数据和模型"]}, {"cell_type": "code", "execution_count": null, "id": "8867917c", "metadata": {}, "outputs": [], "source": ["# 数据路径\n", "data_path = '../ChnSentiCorp_htl_all.csv'\n", "model_save_dir = '../results/models/'\n", "\n", "# 加载数据\n", "print(\"加载和预处理数据...\")\n", "train_data, val_data, test_data = load_and_preprocess_data(data_path)\n", "\n", "# 创建数据加载器\n", "train_loader, val_loader, test_loader = create_data_loaders(\n", "    train_data, val_data, test_data, batch_size=16\n", ")\n", "\n", "print(f\"数据集大小 - 训练: {len(train_data)}, 验证: {len(val_data)}, 测试: {len(test_data)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2caa4b49", "metadata": {}, "outputs": [], "source": ["# 设备配置\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"使用设备: {device}\")\n", "\n", "# 加载训练好的模型\n", "print(\"加载训练好的模型...\")\n", "\n", "# 修正模型保存路径\n", "model_save_dir = '../models/saved_models/'\n", "\n", "# 基础BERT模型\n", "base_model = EnhancedBERTModel().to(device)\n", "base_model_path = os.path.join(model_save_dir, 'best_base_model.pth')\n", "if os.path.exists(base_model_path):\n", "    # 加载checkpoint\n", "    checkpoint = torch.load(base_model_path, map_location=device)\n", "    # 提取模型状态字典\n", "    if 'model_state_dict' in checkpoint:\n", "        base_model.load_state_dict(checkpoint['model_state_dict'])\n", "        print(\"✓ 基础BERT模型加载成功\")\n", "    else:\n", "        # 兼容直接保存state_dict的情况\n", "        base_model.load_state_dict(checkpoint)\n", "        print(\"✓ 基础BERT模型加载成功\")\n", "else:\n", "    print(\"⚠ 基础BERT模型文件不存在，请先运行训练\")\n", "\n", "# 改进BERT模型\n", "improved_model = ImprovedBERTModel().to(device)\n", "improved_model_path = os.path.join(model_save_dir, 'best_improved_model.pth')\n", "if os.path.exists(improved_model_path):\n", "    # 加载checkpoint\n", "    checkpoint = torch.load(improved_model_path, map_location=device)\n", "    # 提取模型状态字典\n", "    if 'model_state_dict' in checkpoint:\n", "        improved_model.load_state_dict(checkpoint['model_state_dict'])\n", "        print(\"✓ 改进BERT模型加载成功\")\n", "    else:\n", "        # 兼容直接保存state_dict的情况\n", "        improved_model.load_state_dict(checkpoint)\n", "        print(\"✓ 改进BERT模型加载成功\")\n", "else:\n", "    print(\"⚠ 改进BERT模型文件不存在，请先运行训练\")"]}, {"cell_type": "markdown", "id": "a95b7ad2", "metadata": {}, "source": ["## 2. 模型性能评估"]}, {"cell_type": "code", "execution_count": null, "id": "59c33733", "metadata": {}, "outputs": [], "source": ["# 创建tokenizer\n", "from transformers import BertTokenizer\n", "tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')\n", "\n", "# 评估基础模型\n", "print(\"=\" * 50)\n", "print(\"基础BERT模型评估结果\")\n", "print(\"=\" * 50)\n", "\n", "if os.path.exists(base_model_path):\n", "    # 创建评估器 - 传入模型和tokenizer\n", "    evaluator = ModelEvaluator(base_model, tokenizer, device)\n", "    \n", "    # 使用正确的方法名evaluate_on_dataset，并获取预测结果\n", "    base_results, base_predictions = evaluator.evaluate_on_dataset(test_loader, return_predictions=True)\n", "    \n", "    print(f\"准确率: {base_results['accuracy']:.4f}\")\n", "    print(f\"精确率: {base_results['precision_macro']:.4f}\")\n", "    print(f\"召回率: {base_results['recall_macro']:.4f}\")\n", "    print(f\"F1分数: {base_results['f1_macro']:.4f}\")\n", "    print(f\"AUC: {base_results['auc']:.4f}\")\n", "    \n", "    # 详细分类报告\n", "    print(\"\\n详细分类报告:\")\n", "    print(classification_report(base_predictions['labels'], base_predictions['predictions'], \n", "                              target_names=['负面', '正面'], digits=4))\n", "else:\n", "    print(\"基础模型未找到，跳过评估\")"]}, {"cell_type": "code", "execution_count": null, "id": "580c11d8", "metadata": {}, "outputs": [], "source": ["# 评估改进模型\n", "print(\"=\" * 50)\n", "print(\"改进BERT模型评估结果\")\n", "print(\"=\" * 50)\n", "\n", "if os.path.exists(improved_model_path):\n", "    # 创建评估器 - 传入模型和tokenizer\n", "    improved_evaluator = ModelEvaluator(improved_model, tokenizer, device)\n", "    \n", "    # 使用正确的方法名evaluate_on_dataset，并获取预测结果\n", "    improved_results, improved_predictions = improved_evaluator.evaluate_on_dataset(test_loader, return_predictions=True)\n", "    \n", "    print(f\"准确率: {improved_results['accuracy']:.4f}\")\n", "    print(f\"精确率: {improved_results['precision_macro']:.4f}\")\n", "    print(f\"召回率: {improved_results['recall_macro']:.4f}\")\n", "    print(f\"F1分数: {improved_results['f1_macro']:.4f}\")\n", "    print(f\"AUC: {improved_results['auc']:.4f}\")\n", "    \n", "    # 详细分类报告\n", "    print(\"\\n详细分类报告:\")\n", "    print(classification_report(improved_predictions['labels'], improved_predictions['predictions'], \n", "                              target_names=['负面', '正面'], digits=4))\n", "else:\n", "    print(\"改进模型未找到，跳过评估\")"]}, {"cell_type": "markdown", "id": "37edd7b4", "metadata": {}, "source": ["## 3. 性能对比分析"]}, {"cell_type": "code", "execution_count": null, "id": "043d1326", "metadata": {}, "outputs": [], "source": ["# 性能对比\n", "if os.path.exists(base_model_path) and os.path.exists(improved_model_path):\n", "    print(\"=\" * 60)\n", "    print(\"模型性能对比\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 创建对比表格\n", "    comparison_data = {\n", "        '指标': ['准确率', '精确率', '召回率', 'F1分数', 'AUC'],\n", "        '基础BERT': [base_results['accuracy'], base_results['precision_macro'], \n", "                    base_results['recall_macro'], base_results['f1_macro'], base_results['auc']],\n", "        '改进BERT': [improved_results['accuracy'], improved_results['precision_macro'], \n", "                    improved_results['recall_macro'], improved_results['f1_macro'], improved_results['auc']]\n", "    }\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    comparison_df['提升'] = comparison_df['改进BERT'] - comparison_df['基础BERT']\n", "    comparison_df['提升率(%)'] = (comparison_df['提升'] / comparison_df['基础BERT'] * 100).round(2)\n", "    \n", "    print(comparison_df.to_string(index=False, float_format='%.4f'))\n", "    \n", "    # 可视化对比\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 指标对比柱状图\n", "    metrics = ['准确率', '精确率', '召回率', 'F1分数', 'AUC']\n", "    base_scores = [base_results['accuracy'], base_results['precision_macro'], \n", "                   base_results['recall_macro'], base_results['f1_macro'], base_results['auc']]\n", "    improved_scores = [improved_results['accuracy'], improved_results['precision_macro'], \n", "                      improved_results['recall_macro'], improved_results['f1_macro'], improved_results['auc']]\n", "    \n", "    x = np.arange(len(metrics))\n", "    width = 0.35\n", "    \n", "    ax1.bar(x - width/2, base_scores, width, label='基础BERT', alpha=0.8)\n", "    ax1.bar(x + width/2, improved_scores, width, label='改进BERT', alpha=0.8)\n", "    ax1.set_ylabel('分数')\n", "    ax1.set_title('模型性能对比')\n", "    ax1.set_xticks(x)\n", "    ax1.set_xticklabels(metrics, rotation=45)\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 提升率柱状图\n", "    improvements = [(improved_scores[i] - base_scores[i]) / base_scores[i] * 100 \n", "                   for i in range(len(metrics))]\n", "    colors = ['green' if x > 0 else 'red' for x in improvements]\n", "    \n", "    ax2.bar(metrics, improvements, color=colors, alpha=0.7)\n", "    ax2.set_ylabel('提升率 (%)')\n", "    ax2.set_title('改进模型相对提升')\n", "    ax2.set_xticklabels(metrics, rotation=45)\n", "    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 在柱状图上显示数值\n", "    for i, v in enumerate(improvements):\n", "        ax2.text(i, v + 0.1 if v > 0 else v - 0.3, f'{v:.2f}%', \n", "                ha='center', va='bottom' if v > 0 else 'top')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 保存对比结果\n", "    comparison_df.to_csv('../results/model_comparison.csv', index=False)\n", "    print(\"\\n对比结果已保存到 results/model_comparison.csv\")"]}, {"cell_type": "markdown", "id": "a473d9ec", "metadata": {}, "source": ["## 4. 混淆矩阵分析"]}, {"cell_type": "code", "execution_count": null, "id": "82976c3c", "metadata": {}, "outputs": [], "source": ["# 绘制混淆矩阵\n", "if os.path.exists(base_model_path) and os.path.exists(improved_model_path):\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    # 基础模型混淆矩阵\n", "    cm_base = confusion_matrix(base_predictions['labels'], base_predictions['predictions'])\n", "    plot_confusion_matrix(cm_base, ['负面', '正面'], ax=ax1, title='基础BERT模型混淆矩阵')\n", "    \n", "    # 改进模型混淆矩阵\n", "    cm_improved = confusion_matrix(improved_predictions['labels'], improved_predictions['predictions'])\n", "    plot_confusion_matrix(cm_improved, ['负面', '正面'], ax=ax2, title='改进BERT模型混淆矩阵')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 分析混淆矩阵\n", "    print(\"混淆矩阵分析:\")\n", "    print(f\"基础模型 - 真负例: {cm_base[0,0]}, 假正例: {cm_base[0,1]}, 假负例: {cm_base[1,0]}, 真正例: {cm_base[1,1]}\")\n", "    print(f\"改进模型 - 真负例: {cm_improved[0,0]}, 假正例: {cm_improved[0,1]}, 假负例: {cm_improved[1,0]}, 真正例: {cm_improved[1,1]}\")\n", "    \n", "    # 错误分析\n", "    base_fn_rate = cm_base[1,0] / (cm_base[1,0] + cm_base[1,1])  # 假负例率\n", "    base_fp_rate = cm_base[0,1] / (cm_base[0,1] + cm_base[0,0])  # 假正例率\n", "    \n", "    improved_fn_rate = cm_improved[1,0] / (cm_improved[1,0] + cm_improved[1,1])\n", "    improved_fp_rate = cm_improved[0,1] / (cm_improved[0,1] + cm_improved[0,0])\n", "    \n", "    print(f\"\\n错误率分析:\")\n", "    print(f\"假负例率 - 基础: {base_fn_rate:.4f}, 改进: {improved_fn_rate:.4f}, 改善: {base_fn_rate-improved_fn_rate:.4f}\")\n", "    print(f\"假正例率 - 基础: {base_fp_rate:.4f}, 改进: {improved_fp_rate:.4f}, 改善: {base_fp_rate-improved_fp_rate:.4f}\")"]}, {"cell_type": "markdown", "id": "c98e98e9", "metadata": {}, "source": ["## 5. ROC曲线分析"]}, {"cell_type": "code", "execution_count": null, "id": "1bfe9584", "metadata": {}, "outputs": [], "source": ["# 绘制ROC曲线\n", "if os.path.exists(base_model_path) and os.path.exists(improved_model_path):\n", "    # 检查预测结果是否存在\n", "    if 'base_predictions' in locals() and 'improved_predictions' in locals():\n", "        plt.figure(figsize=(10, 8))\n", "        \n", "        # 基础模型ROC\n", "        fpr_base, tpr_base, _ = roc_curve(base_predictions['labels'], np.array(base_predictions['probabilities'])[:, 1])\n", "        roc_auc_base = auc(fpr_base, tpr_base)\n", "        \n", "        # 改进模型ROC\n", "        fpr_improved, tpr_improved, _ = roc_curve(improved_predictions['labels'], np.array(improved_predictions['probabilities'])[:, 1])\n", "        roc_auc_improved = auc(fpr_improved, tpr_improved)\n", "        \n", "        # 绘制ROC曲线\n", "        plt.plot(fpr_base, tpr_base, color='blue', lw=2, \n", "                 label=f'基础BERT (AUC = {roc_auc_base:.4f})')\n", "        plt.plot(fpr_improved, tpr_improved, color='red', lw=2, \n", "                 label=f'改进BERT (AUC = {roc_auc_improved:.4f})')\n", "        plt.plot([0, 1], [0, 1], color='gray', lw=2, linestyle='--', label='随机分类器')\n", "        \n", "        plt.xlim([0.0, 1.0])\n", "        plt.ylim([0.0, 1.05])\n", "        plt.xlabel('假正例率 (FPR)')\n", "        plt.ylabel('真正例率 (TPR)')\n", "        plt.title('ROC曲线对比')\n", "        plt.legend(loc=\"lower right\")\n", "        plt.grid(True, alpha=0.3)\n", "        plt.show()\n", "        \n", "        print(f\"AUC提升: {roc_auc_improved - roc_auc_base:.4f}\")\n", "    else:\n", "        print(\"⚠ 预测结果不存在，请先运行前面的模型评估单元格\")\n", "else:\n", "    print(\"⚠ 模型文件不存在，请先运行模型训练\")"]}, {"cell_type": "markdown", "id": "6230a538", "metadata": {}, "source": ["## 6. 预测置信度分析"]}, {"cell_type": "code", "execution_count": null, "id": "b5f58b5c", "metadata": {}, "outputs": [], "source": ["# 置信度分析\n", "if os.path.exists(improved_model_path):\n", "    print(\"预测置信度分析\")\n", "    print(\"=\" * 30)\n", "    \n", "    # 检查变量是否存在\n", "    if 'improved_predictions' in locals() and improved_predictions is not None:\n", "        # 获取预测概率\n", "        probs = np.array(improved_predictions['probabilities'])\n", "        max_probs = np.max(probs, axis=1)  # 最大概率作为置信度\n", "        predictions = np.array(improved_predictions['predictions'])\n", "        true_labels = np.array(improved_predictions['labels'])\n", "    \n", "    # 正确和错误预测的置信度\n", "    correct_mask = (predictions == true_labels)\n", "    correct_confidence = max_probs[correct_mask]\n", "    incorrect_confidence = max_probs[~correct_mask]\n", "    \n", "    print(f\"正确预测平均置信度: {np.mean(correct_confidence):.4f} ± {np.std(correct_confidence):.4f}\")\n", "    print(f\"错误预测平均置信度: {np.mean(incorrect_confidence):.4f} ± {np.std(incorrect_confidence):.4f}\")\n", "    \n", "    # 置信度分布图\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 置信度直方图\n", "    ax1.hist(correct_confidence, bins=30, alpha=0.7, label='正确预测', color='green')\n", "    ax1.hist(incorrect_confidence, bins=30, alpha=0.7, label='错误预测', color='red')\n", "    ax1.set_xlabel('置信度')\n", "    ax1.set_ylabel('频次')\n", "    ax1.set_title('预测置信度分布')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 置信度区间准确率\n", "    confidence_bins = np.arange(0.5, 1.01, 0.05)\n", "    bin_accuracies = []\n", "    bin_counts = []\n", "    \n", "    for i in range(len(confidence_bins)-1):\n", "        mask = (max_probs >= confidence_bins[i]) & (max_probs < confidence_bins[i+1])\n", "        if np.sum(mask) > 0:\n", "            accuracy = np.mean(predictions[mask] == true_labels[mask])\n", "            bin_accuracies.append(accuracy)\n", "            bin_counts.append(np.sum(mask))\n", "        else:\n", "            bin_accuracies.append(0)\n", "            bin_counts.append(0)\n", "    \n", "    bin_centers = (confidence_bins[:-1] + confidence_bins[1:]) / 2\n", "    ax2.bar(bin_centers, bin_accuracies, width=0.04, alpha=0.7, color='skyblue')\n", "    ax2.set_xlabel('置信度区间')\n", "    ax2.set_ylabel('准确率')\n", "    ax2.set_title('不同置信度区间的准确率')\n", "    ax2.set_ylim([0, 1])\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 在柱状图上显示样本数量\n", "    for i, (acc, count) in enumerate(zip(bin_accuracies, bin_counts)):\n", "        if count > 0:\n", "            ax2.text(bin_centers[i], acc + 0.02, str(count), \n", "                    ha='center', va='bottom', fontsize=8)\n", "    \n", "        plt.tight_layout()\n", "        plt.show()\n", "    else:\n", "        print(\"预测结果不可用，跳过置信度分析\")"]}, {"cell_type": "markdown", "id": "1612b070", "metadata": {}, "source": ["## 7. 错误案例分析"]}, {"cell_type": "code", "execution_count": null, "id": "751ef80f", "metadata": {}, "outputs": [], "source": ["# 错误案例分析\n", "if os.path.exists(improved_model_path):\n", "    print(\"错误案例分析\")\n", "    print(\"=\" * 30)\n", "    \n", "    # 获取测试数据文本信息\n", "    test_texts = [item['text'] for item in test_data]\n", "    \n", "    # 使用已经获取的预测结果\n", "    probs = np.array(improved_predictions['probabilities'])\n", "    max_probs = np.max(probs, axis=1)  # 最大概率作为置信度\n", "    predictions = np.array(improved_predictions['predictions'])\n", "    true_labels = np.array(improved_predictions['labels'])\n", "    \n", "    # 找到错误预测的样本\n", "    incorrect_indices = np.where(predictions != true_labels)[0]\n", "    \n", "    print(f\"总共 {len(incorrect_indices)} 个错误预测\")\n", "    \n", "    # 分析假正例（预测正面，实际负面）\n", "    fp_indices = incorrect_indices[(predictions[incorrect_indices] == 1) & (true_labels[incorrect_indices] == 0)]\n", "    print(f\"\\n假正例数量: {len(fp_indices)}\")\n", "    print(\"假正例样本（预测正面，实际负面）:\")\n", "    print(\"-\" * 50)\n", "    \n", "    for i, idx in enumerate(fp_indices[:5]):  # 显示前5个\n", "        confidence = max_probs[idx]\n", "        text = test_texts[idx][:100] + \"...\" if len(test_texts[idx]) > 100 else test_texts[idx]\n", "        print(f\"{i+1}. 置信度: {confidence:.4f}\")\n", "        print(f\"   文本: {text}\")\n", "        print()\n", "    \n", "    # 分析假负例（预测负面，实际正面）\n", "    fn_indices = incorrect_indices[(predictions[incorrect_indices] == 0) & (true_labels[incorrect_indices] == 1)]\n", "    print(f\"\\n假负例数量: {len(fn_indices)}\")\n", "    print(\"假负例样本（预测负面，实际正面）:\")\n", "    print(\"-\" * 50)\n", "    \n", "    for i, idx in enumerate(fn_indices[:5]):  # 显示前5个\n", "        confidence = max_probs[idx]\n", "        text = test_texts[idx][:100] + \"...\" if len(test_texts[idx]) > 100 else test_texts[idx]\n", "        print(f\"{i+1}. 置信度: {confidence:.4f}\")\n", "        print(f\"   文本: {text}\")\n", "        print()\n", "    \n", "    # 错误类型统计\n", "    error_analysis = {\n", "        '错误类型': ['假正例(FP)', '假负例(FN)'],\n", "        '数量': [len(fp_indices), len(fn_indices)],\n", "        '占总错误比例': [len(fp_indices)/len(incorrect_indices)*100, len(fn_indices)/len(incorrect_indices)*100],\n", "        '平均置信度': [np.mean(max_probs[fp_indices]) if len(fp_indices) > 0 else 0,\n", "                     np.mean(max_probs[fn_indices]) if len(fn_indices) > 0 else 0]\n", "    }\n", "    \n", "    error_df = pd.DataFrame(error_analysis)\n", "    print(\"\\n错误类型统计:\")\n", "    print(error_df.to_string(index=False, float_format='%.2f'))"]}, {"cell_type": "markdown", "id": "7529c27b", "metadata": {}, "source": ["## 8. 注意力机制可视化"]}, {"cell_type": "code", "execution_count": null, "id": "8a1ba68a", "metadata": {}, "outputs": [], "source": ["# 注意力可视化（仅针对改进模型）\n", "if os.path.exists(improved_model_path):\n", "    print(\"注意力机制可视化\")\n", "    print(\"=\" * 30)\n", "    \n", "    # 选择几个样本进行注意力可视化\n", "    sample_indices = [0, 10, 20]  # 选择前几个测试样本\n", "    \n", "    # 使用已经获取的预测结果\n", "    predictions = np.array(improved_predictions['predictions'])\n", "    true_labels = np.array(improved_predictions['labels'])\n", "    probs = np.array(improved_predictions['probabilities'])\n", "    max_probs = np.max(probs, axis=1)\n", "    \n", "    improved_model.eval()\n", "    with torch.no_grad():\n", "        for i, sample_idx in enumerate(sample_indices):\n", "            # 获取单个样本\n", "            sample_data = test_data[sample_idx]\n", "            input_ids = torch.tensor(sample_data['input_ids']).unsqueeze(0).to(device)\n", "            attention_mask = torch.tensor(sample_data['attention_mask']).unsqueeze(0).to(device)\n", "            \n", "            # 前向传播获取注意力权重（不需要return_attention参数）\n", "            outputs = improved_model(input_ids, attention_mask)\n", "            \n", "            if 'attention_weights' in outputs:\n", "                # 注意力权重的形状是 [batch_size, num_heads, seq_len, seq_len]\n", "                attention_weights = outputs['attention_weights'].cpu().numpy()[0]  # [num_heads, seq_len, seq_len]\n", "                \n", "                # 取所有头的平均注意力权重\n", "                avg_attention = np.mean(attention_weights, axis=0)  # [seq_len, seq_len]\n", "                \n", "                # 获取对应的token\n", "                from transformers import BertTokenizer\n", "                tokenizer = BertTokenizer.from_pretrained('bert-base-chinese')\n", "                tokens = tokenizer.convert_ids_to_tokens(input_ids[0].cpu().numpy())\n", "                \n", "                # 去除padding tokens\n", "                valid_len = torch.sum(attention_mask[0]).item()\n", "                tokens = tokens[:valid_len]\n", "                avg_attention = avg_attention[:valid_len, :valid_len]\n", "                \n", "                # 可视化注意力矩阵\n", "                plt.figure(figsize=(12, 10))\n", "                sns.heatmap(avg_attention, \n", "                           xticklabels=tokens, yticklabels=tokens,\n", "                           cmap='Blues', cbar=True)\n", "                plt.title(f'样本 {sample_idx+1} 注意力权重矩阵（平均所有头）\\n预测: {\"正面\" if predictions[sample_idx] else \"负面\"}, 实际: {\"正面\" if true_labels[sample_idx] else \"负面\"}')\n", "                plt.xlabel('Key Tokens')\n", "                plt.ylabel('Query Tokens')\n", "                plt.xticks(rotation=45, ha='right')\n", "                plt.yticks(rotation=0)\n", "                plt.tight_layout()\n", "                plt.show()\n", "                \n", "                # 显示文本内容\n", "                print(f\"\\n样本 {sample_idx+1} 原文:\")\n", "                print(sample_data['text'][:200] + \"...\" if len(sample_data['text']) > 200 else sample_data['text'])\n", "                print(f\"预测: {'正面' if predictions[sample_idx] else '负面'} (置信度: {max_probs[sample_idx]:.4f})\")\n", "                print(f\"实际: {'正面' if true_labels[sample_idx] else '负面'}\")\n", "                print(\"-\" * 80)\n", "                \n", "                # 可选：显示单个注意力头的权重\n", "                if attention_weights.shape[0] > 1:  # 如果有多个注意力头\n", "                    num_heads_to_show = min(4, attention_weights.shape[0])\n", "                    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "                    axes = axes.flatten()\n", "                    \n", "                    for head_idx in range(num_heads_to_show):\n", "                        head_attention = attention_weights[head_idx][:valid_len, :valid_len]\n", "                        sns.heatmap(head_attention, \n", "                                   xticklabels=tokens, yticklabels=tokens,\n", "                                   cmap='Blues', cbar=True, ax=axes[head_idx])\n", "                        axes[head_idx].set_title(f'注意力头 {head_idx+1}')\n", "                        axes[head_idx].set_xlabel('Key Tokens')\n", "                        axes[head_idx].set_ylabel('Query Tokens')\n", "                        axes[head_idx].tick_params(axis='x', rotation=45)\n", "                    \n", "                    plt.suptitle(f'样本 {sample_idx+1} - 各个注意力头的权重分布')\n", "                    plt.tight_layout()\n", "                    plt.show()\n", "            else:\n", "                print(f\"样本 {sample_idx+1} 没有返回注意力权重\")\n", "    \n", "    print(\"注意力可视化完成！\")\n", "else:\n", "    print(\"改进模型文件不存在，请先运行模型训练\")"]}, {"cell_type": "markdown", "id": "93b02ef3", "metadata": {}, "source": ["## 9. 保存评估结果"]}, {"cell_type": "code", "execution_count": null, "id": "d1a6b506", "metadata": {}, "outputs": [], "source": ["# 保存详细评估结果\n", "if os.path.exists(base_model_path) and os.path.exists(improved_model_path):\n", "    # 使用已经计算的变量\n", "    probs = np.array(improved_predictions['probabilities'])\n", "    max_probs = np.max(probs, axis=1)\n", "    predictions = np.array(improved_predictions['predictions'])\n", "    true_labels = np.array(improved_predictions['labels'])\n", "    test_texts = [item['text'] for item in test_data]\n", "    \n", "    # 计算错误样本\n", "    incorrect_indices = np.where(predictions != true_labels)[0]\n", "    fp_indices = incorrect_indices[(predictions[incorrect_indices] == 1) & (true_labels[incorrect_indices] == 0)]\n", "    fn_indices = incorrect_indices[(predictions[incorrect_indices] == 0) & (true_labels[incorrect_indices] == 1)]\n", "    \n", "    # 计算置信度统计\n", "    correct_mask = (predictions == true_labels)\n", "    correct_confidence = max_probs[correct_mask]\n", "    incorrect_confidence = max_probs[~correct_mask]\n", "    \n", "    # 创建详细结果字典\n", "    detailed_results = {\n", "        'base_model_results': {\n", "            'accuracy': float(base_results['accuracy']),\n", "            'precision': float(base_results['precision_macro']),\n", "            'recall': float(base_results['recall_macro']),\n", "            'f1': float(base_results['f1_macro']),\n", "            'auc': float(base_results['auc'])\n", "        },\n", "        'improved_model_results': {\n", "            'accuracy': float(improved_results['accuracy']),\n", "            'precision': float(improved_results['precision_macro']),\n", "            'recall': float(improved_results['recall_macro']),\n", "            'f1': float(improved_results['f1_macro']),\n", "            'auc': float(improved_results['auc'])\n", "        },\n", "        'comparison': comparison_df.to_dict(),\n", "        'error_analysis': {\n", "            'total_errors': len(incorrect_indices),\n", "            'false_positives': len(fp_indices),\n", "            'false_negatives': len(fn_indices),\n", "            'fp_examples': [test_texts[idx][:200] for idx in fp_indices[:5]],\n", "            'fn_examples': [test_texts[idx][:200] for idx in fn_indices[:5]]\n", "        },\n", "        'confidence_analysis': {\n", "            'correct_confidence_mean': float(np.mean(correct_confidence)),\n", "            'correct_confidence_std': float(np.std(correct_confidence)),\n", "            'incorrect_confidence_mean': float(np.mean(incorrect_confidence)),\n", "            'incorrect_confidence_std': float(np.std(incorrect_confidence))\n", "        }\n", "    }\n", "    \n", "    # 保存为JSON文件\n", "    import json\n", "    with open('../results/detailed_evaluation_results.json', 'w', encoding='utf-8') as f:\n", "        json.dump(detailed_results, f, ensure_ascii=False, indent=2, default=str)\n", "    \n", "    print(\"详细评估结果已保存到 results/detailed_evaluation_results.json\")\n", "    \n", "    # 生成评估报告摘要\n", "    base_model_results = detailed_results['base_model_results']\n", "    improved_model_results = detailed_results['improved_model_results']\n", "    \n", "    summary_report = f\"\"\"\n", "# 中文酒店评论情感分析模型评估报告\n", "\n", "## 模型性能对比\n", "\n", "| 指标 | 基础BERT | 改进BERT | 提升 | 提升率 |\n", "|------|----------|----------|------|--------|\n", "| 准确率 | {base_model_results['accuracy']:.4f} | {improved_model_results['accuracy']:.4f} | {improved_model_results['accuracy']-base_model_results['accuracy']:.4f} | {(improved_model_results['accuracy']-base_model_results['accuracy'])/base_model_results['accuracy']*100:.2f}% |\n", "| 精确率 | {base_model_results['precision']:.4f} | {improved_model_results['precision']:.4f} | {improved_model_results['precision']-base_model_results['precision']:.4f} | {(improved_model_results['precision']-base_model_results['precision'])/base_model_results['precision']*100:.2f}% |\n", "| 召回率 | {base_model_results['recall']:.4f} | {improved_model_results['recall']:.4f} | {improved_model_results['recall']-base_model_results['recall']:.4f} | {(improved_model_results['recall']-base_model_results['recall'])/base_model_results['recall']*100:.2f}% |\n", "| F1分数 | {base_model_results['f1']:.4f} | {improved_model_results['f1']:.4f} | {improved_model_results['f1']-base_model_results['f1']:.4f} | {(improved_model_results['f1']-base_model_results['f1'])/base_model_results['f1']*100:.2f}% |\n", "| AUC | {base_model_results['auc']:.4f} | {improved_model_results['auc']:.4f} | {improved_model_results['auc']-base_model_results['auc']:.4f} | {(improved_model_results['auc']-base_model_results['auc'])/base_model_results['auc']*100:.2f}% |\n", "\n", "## 错误分析\n", "\n", "- 总错误数: {len(incorrect_indices)}\n", "- 假正例数: {len(fp_indices)} ({len(fp_indices)/len(incorrect_indices)*100:.1f}%)\n", "- 假负例数: {len(fn_indices)} ({len(fn_indices)/len(incorrect_indices)*100:.1f}%)\n", "\n", "## 置信度分析\n", "\n", "- 正确预测平均置信度: {np.mean(correct_confidence):.4f} ± {np.std(correct_confidence):.4f}\n", "- 错误预测平均置信度: {np.mean(incorrect_confidence):.4f} ± {np.std(incorrect_confidence):.4f}\n", "\n", "## 结论\n", "\n", "改进的BERT模型通过引入BiLSTM、多头注意力机制和对比学习，在所有评估指标上都实现了显著提升。\n", "模型在置信度校准方面表现良好，正确预测的置信度明显高于错误预测。\n", "\"\"\"\n", "    \n", "    with open('../results/evaluation_summary.md', 'w', encoding='utf-8') as f:\n", "        f.write(summary_report)\n", "    \n", "    print(\"评估报告摘要已保存到 results/evaluation_summary.md\")\n", "    print(\"\\n评估分析完成！\")\n", "else:\n", "    print(\"模型文件不存在，请先运行模型训练\")"]}], "metadata": {"kernelspec": {"display_name": "bert_sentiment_analysis", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.23"}}, "nbformat": 4, "nbformat_minor": 5}